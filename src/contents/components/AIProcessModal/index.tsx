import React, { useEffect, useRef, useState, useCallback } from 'react';
import { LeftOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';
import { ConfigProvider, Divider } from 'antd';
import { chatLogo } from '../../../common/images';
import * as styles from "./index.module.less";
import { getAIService } from '../../config/aiConfig';
import SecondaryActionButtons from '../SecondaryActionButtons';
import type { SecondaryAction } from '../../../config/menuItems';
import { textOperations } from '../../utils/textOperations';
import LanguageSelector, { type LanguageSelection } from '../LanguageSelector';

interface AIProcessModalProps {
  isVisible: boolean;
  selectedText: string;
  actionType: string;
  onClose: () => void;
  onRegisterDataUpdater?: (updater: (data: string, isComplete: boolean, conversationId?: string) => void) => void;
  onRetriggerTranslation?: (languageOptions: LanguageSelection) => void;
}

const AIProcessModal: React.FC<AIProcessModalProps> = ({
  isVisible,
  selectedText,
  actionType,
  onClose,
  onRegisterDataUpdater,
  onRetriggerTranslation
}) => {
  // 自定义状态管理，替代 useAIProcess hook
  const [state, setState] = useState({
    isProcessing: false,
    content: '',
    isComplete: false,
    error: null as string | null,
    processingTime: 0,
    conversationId: null as string | null,
  });

  const modalRef = useRef<HTMLDivElement>(null);
  const contentAreaRef = useRef<HTMLDivElement>(null); // 内容区域引用
  const aiService = getAIService();
  const processStartedRef = useRef<string>(''); // 记录已启动的处理
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [isThinkingExpanded, setIsThinkingExpanded] = useState(false); // 思考过程展开状态
  const [showContinueInput, setShowContinueInput] = useState(false); // 是否显示继续问输入框
  const [continueQuestion, setContinueQuestion] = useState(''); // 继续问的内容
  const [isClosing, setIsClosing] = useState(false); // 是否正在关闭（用于淡出动画）
  const [isUserScrolling, setIsUserScrolling] = useState(false); // 用户是否正在手动滚动
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null); // 滚动超时引用
  const [currentLanguageSelection, setCurrentLanguageSelection] = useState<LanguageSelection>({
    srcLang: 'auto',
    tgtLang: 'zh'
  }); // 当前语言选择

  // 获取操作类型的中文名称
  const getActionName = (action: string): string => {
    return aiService.getActionName(action);
  };

  // 自动滚动到底部
  const scrollToBottom = useCallback((smooth: boolean = true) => {
    if (contentAreaRef.current) {
      const scrollOptions: ScrollToOptions = {
        top: contentAreaRef.current.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto'
      };
      contentAreaRef.current.scrollTo(scrollOptions);
    }
  }, []);

  // 检查是否已滚动到底部
  const isScrolledToBottom = useCallback(() => {
    if (!contentAreaRef.current) return true;
    const { scrollTop, scrollHeight, clientHeight } = contentAreaRef.current;
    return Math.abs(scrollTop + clientHeight - scrollHeight) <= 0;
  }, []);

  // 处理用户滚动事件
  const handleScroll = useCallback(() => {
    if (!contentAreaRef.current) return;

    // 清除之前的超时
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 检查是否滚动到底部
    const atBottom = isScrolledToBottom();

    if (!atBottom) {
      // 用户不在底部，立即标记为手动滚动状态
      setIsUserScrolling(true);

      // 设置较短的超时，用于检测用户是否停止滚动
      scrollTimeoutRef.current = setTimeout(() => {
        // 再次检查是否在底部，如果是则重置手动滚动状态
        if (isScrolledToBottom()) {
          setIsUserScrolling(false);
        }
      }, 500); // 减少到500ms，更快响应用户回到底部的操作
    } else {
      // 用户在底部，立即重置手动滚动状态
      setIsUserScrolling(false);
    }
  }, [isScrolledToBottom]);

  // 自定义状态管理函数
  const startProcess = () => {
    setState({
      isProcessing: true,
      content: '',
      isComplete: false,
      error: null,
      processingTime: 0,
      conversationId: null,
    });

    // 启动计时器
    timerRef.current = setInterval(() => {
      setState(prev => ({
        ...prev,
        processingTime: prev.processingTime + 1
      }));
    }, 1000);
  };

  const stopProcess = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
      scrollTimeoutRef.current = null;
    }
    setState(prev => ({
      ...prev,
      isProcessing: false,
    }));
  };

  const resetState = () => {
    setState({
      isProcessing: false,
      content: '',
      isComplete: false,
      error: null,
      processingTime: 0,
      conversationId: null,
    });
  };

  // 处理关闭
  const handleClose = () => {
    setIsClosing(true);
    // 延迟执行关闭，让淡出动画播放
    setTimeout(() => {
      stopProcess();
      resetState();
      setIsClosing(false);
      onClose();
    }, 200); // 与CSS动画时长匹配
  };

  // 处理语言选择变更
  const handleLanguageChange = useCallback((selection: LanguageSelection) => {
    console.log('AIProcessModal: handleLanguageChange called', selection);
    setCurrentLanguageSelection(selection);

    // 如果是翻译操作且有重新触发翻译的回调，则触发重新翻译
    if (actionType === 'translate' && onRetriggerTranslation && typeof onRetriggerTranslation === 'function') {
      onRetriggerTranslation(selection);
    }
  }, [actionType, onRetriggerTranslation]); // 使用useCallback优化性能



  // 继续问
  const handleContinueAsk = () => {
    console.log('handleContinueAsk');
    setShowContinueInput(true);
  };

  // 发送继续问题
  const handleSendContinueQuestion = () => {
    if (!continueQuestion.trim()) return;

    // 向背景脚本发送消息，请求打开侧边栏并传递问题
    chrome.runtime.sendMessage({
      action: 'CONTINUE_ASK_TO_BACKGROUND',
      question: continueQuestion.trim(),
      originalText: selectedText,
      originalAction: actionType,
      conversationId: state.conversationId // 传递保存的 conversationId
    });

    // 关闭弹窗
    handleClose();
  };

  // 处理输入框回车事件
  const handleContinueInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendContinueQuestion();
    }
  };

  // 调整
  const handleAdjust = () => {
    // 显示调整选项或重新生成
    setShowContinueInput(true);
    setContinueQuestion('请调整上述内容，使其更加...');
  };

  // 弃用
  const handleDiscard = () => {
    // 关闭弹窗，不保存结果
    textOperations.clearCache();
    handleClose();
  };

  // 插入到下方
  const handleInsertBelow = async () => {
    if (!state.content) {
      console.warn('No content to insert');
      return;
    }

    try {
      const result = textOperations.insertTextBelow(state.content);
      if (result.success) {
        console.log('Text inserted successfully:', result.message);
        // 可以显示成功提示
        handleClose();
      } else {
        console.error('Insert failed:', result.error);
        // 可以显示错误提示
        alert(`插入失败: ${result.error}`);
      }
    } catch (error) {
      console.error('Insert operation failed:', error);
      alert('插入操作失败，请重试');
    }
  };

  // 替换原文
  const handleReplace = async () => {
    if (!state.content) {
      console.warn('No content to replace with');
      return;
    }

    try {
      const result = textOperations.replaceSelectedText(state.content);
      if (result.success) {
        console.log('Text replaced successfully:', result.message);
        // 可以显示成功提示
        handleClose();
      } else {
        console.error('Replace failed:', result.error);
        // 可以显示错误提示
        alert(`替换失败: ${result.error}`);
      }
    } catch (error) {
      console.error('Replace operation failed:', error);
      alert('替换操作失败，请重试');
    }
  };

  // 处理二级操作
  const handleSecondaryAction = (action: SecondaryAction) => {
    switch (action.id) {
      case 'continue-asking':
        handleContinueAsk();
        break;
      case 'adjust':
        handleAdjust();
        break;
      case 'deprecate':
        handleDiscard();
        break;
      case 'insert-below':
        handleInsertBelow();
        break;
      case 'replace-original':
        handleReplace();
        break;
      default:
        console.warn('未知的二级操作:', action);
    }
  };

  // 公开的数据更新方法
  const updateModalData = (data: string, isComplete: boolean, conversationId?: string) => {
    // 在更新状态前检查用户是否在底部
    const wasAtBottom = isScrolledToBottom();

    setState(prev => ({
      ...prev,
      content: data,
      isComplete: isComplete,
      isProcessing: !isComplete,
      // 只在首次接收到 conversationId 时更新，避免覆盖已有值
      conversationId: conversationId || prev.conversationId,
    }));

    if (isComplete && timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // 智能自动滚动逻辑：
    // 1. 如果用户之前在底部且没有手动滚动，则自动滚动
    // 2. 如果用户正在手动滚动，则不自动滚动
    // 使用 requestAnimationFrame 确保 DOM 更新后再滚动
    requestAnimationFrame(() => {
      if (wasAtBottom && !isUserScrolling) {
        scrollToBottom(true);
      }
    });
  };

  // 将数据更新方法注册给父组件
  useEffect(() => {
    if (onRegisterDataUpdater) {
      onRegisterDataUpdater(updateModalData);
    }
  }, [onRegisterDataUpdater]);

  // 监听内容变化，确保初始内容显示时滚动到底部
  useEffect(() => {
    if (state.content && !isUserScrolling) {
      // 使用较短的延迟确保内容已渲染
      const timeoutId = setTimeout(() => {
        scrollToBottom(false); // 初始滚动不使用动画
      }, 50);

      return () => clearTimeout(timeoutId);
    }
  }, [state.content, isUserScrolling, scrollToBottom]);

  // 当组件显示时开始处理
  useEffect(() => {
    const processKey = `${actionType}-${selectedText}`;

    if (isVisible && selectedText && actionType && processStartedRef.current !== processKey) {
      processStartedRef.current = processKey;

      // 缓存当前选择状态，用于后续的文本操作
      textOperations.cacheCurrentSelection();

      // 开始处理（不再调用 AI 服务，而是等待事件数据）
      startProcess();
    }

    return () => {
      if (!isVisible) {
        processStartedRef.current = '';
        stopProcess();
        // 清理缓存的选择状态
        textOperations.clearCache();
        // 重置滚动状态
        setIsUserScrolling(false);
      }
    };
  }, [isVisible, selectedText, actionType]);


  if (!isVisible && !isClosing) return null;

  return (
    <div
      className={`${styles.modalOverlay} ${isClosing ? styles.closing : ''}`}
      onClick={(e) => {
        // 只有当点击的是overlay本身时才关闭弹窗
        if (e.target === e.currentTarget) {
          handleClose();
        }
      }}
    >
      <div
        ref={modalRef}
        className={`${styles.modal} ${isClosing ? styles.closing : ''}`}
        onClick={(e) => {
          // 统一在modal容器级别阻止事件冒泡到overlay
          // 这样点击modal内的任何内容都不会关闭弹窗
          e.stopPropagation();
        }}
      >
        {/* 头部 */}
        <div className={styles.modalHeader}>
          <div className={styles.headerLeft}>
            <div className={styles.aiIcon}>
              <img src={chatLogo} alt="AI助手" />
            </div>
            <span className={styles.actionTitle}>{getActionName(actionType)}</span>
          </div>
          <button
            className={styles.closeButton}
            onClick={handleClose}
          >
            ×
          </button>
        </div>
        {/* 错误状态 */}
        {state.error && (
          <div className={styles.errorMessage}>
            处理失败：{state.error}
          </div>
        )}

        {/* 主要内容区域 */}
        <div
          ref={contentAreaRef}
          className={styles.contentArea}
          onScroll={handleScroll}
        >
          {actionType === 'translate' && (
            <div className={styles.languageSelectorSection}>
              <LanguageSelector
                onLanguageChange={handleLanguageChange}
                defaultSrcLang={currentLanguageSelection.srcLang}
                defaultTgtLang={currentLanguageSelection.tgtLang}
              />
            </div>
          )}
          {state.content && (
            <div className={styles.resultContent}>
              {state.content}
              {state.isProcessing && <span className={styles.cursor}>|</span>}
            </div>
          )}

          {state.isComplete && state.content && (
            <div className={styles.contentFooter}>
              <span className={styles.contentStats}>已生成内容 {state.content.length} 字</span>
            </div>
          )}
        </div>

        {/* 继续问输入框 - 水平布局 */}
        {showContinueInput && (
          <>
            <div className={styles.continueInputSection}>
              <div className={styles.continueInputHorizontal}>
                {/* 左侧返回按钮 */}
                <button
                  className={styles.backButton}
                  onClick={() => setShowContinueInput(false)}
                >
                  <LeftOutlined className={styles.backIcon} />
                  <span className={styles.backText}>返回</span>
                </button>

                {/* 右侧输入框 */}
                <div className={styles.inputContainer}>
                  <input
                    className={styles.continueInputHorizontalInput}
                    value={continueQuestion}
                    onChange={(e) => setContinueQuestion(e.target.value)}
                    onKeyDown={handleContinueInputKeyDown}
                    placeholder="请输入您想继续询问的问题..."
                    autoFocus
                  />
                </div>
              </div>
            </div>
          </>
        )}

        {/* 底部操作按钮 */}
        {state.isComplete && !state.error && !showContinueInput && (
          <SecondaryActionButtons
            menuItemId={actionType}
            onAction={handleSecondaryAction}
            disabled={state.isProcessing}
          />
        )}
      </div>
    </div>
  );
};

export default AIProcessModal;
