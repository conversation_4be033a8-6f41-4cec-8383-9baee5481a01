.customSelect {
  position: relative;
  display: inline-block;
  width: 100%;
}

.selectTrigger {
  /* 重置默认样式 */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;

  /* 基础样式 - 参考 antd Select */
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-width: 80px;
  height: 32px;
  padding: 4px 11px;
  font-size: 14px;
  line-height: 1.5714285714285714;
  color: rgba(0, 0, 0, 0.88);
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  outline: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

  /* 字体设置 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  &:hover {
    border-color: #4096ff;
  }

  &:focus {
    border-color: #4096ff;
    box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2);
  }

  &.open {
    border-color: #4096ff;
    box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2);
  }

  &.disabled {
    color: rgba(0, 0, 0, 0.25);
    background-color: rgba(0, 0, 0, 0.04);
    border-color: #d9d9d9;
    cursor: not-allowed;

    &:hover {
      border-color: #d9d9d9;
    }
  }
}

.selectValue {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &.placeholder {
    color: rgba(0, 0, 0, 0.25);
  }
}

.selectArrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.25);
  transition: transform 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

  &.open {
    transform: rotate(180deg);
  }

  svg {
    width: 12px;
    height: 12px;
  }
}

.selectDropdown {
  /* 参考 SelectionBar 下拉菜单样式 */
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin-top: 4px;
  z-index: 10000;
  max-height: 200px;
  overflow-y: auto;

  /* 添加下拉动画 */
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px) scale(0.95);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  /* 当显示时的状态 */
  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
  }
}

.selectOption {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  &.selected {
    background-color: #e6f4ff;
    color: #1677ff;
    font-weight: 600;
  }

  &:first-child {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
  }

  &:last-child {
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
  }
}

.selectEmpty {
  padding: 8px 12px;
  color: rgba(0, 0, 0, 0.25);
  font-size: 14px;
  text-align: center;
}

/* 滚动条样式 */
.selectDropdown::-webkit-scrollbar {
  width: 6px;
}

.selectDropdown::-webkit-scrollbar-track {
  background: transparent;
}

.selectDropdown::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.selectDropdown::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}
