import React, { useState, useRef, useEffect } from 'react';
import * as styles from './index.module.less';

export interface SelectOption {
  label: string;
  value: string;
}

export interface CustomSelectProps {
  value?: string;
  onChange?: (value: string) => void;
  options?: SelectOption[];
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  value,
  onChange,
  options = [],
  placeholder = '请选择',
  disabled = false,
  className = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 获取当前选中项的标签
  const selectedOption = options.find(option => option.value === value);
  const displayText = selectedOption ? selectedOption.label : placeholder;

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // 处理选择器点击
  const handleSelectClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault()
    e.nativeEvent.stopImmediatePropagation()
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  // 处理选项点击
  const handleOptionClick = (optionValue: string, e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault()
    e.nativeEvent.stopImmediatePropagation()
    if (onChange) {
      onChange(optionValue);
    }
    setIsOpen(false);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        setIsOpen(!isOpen);
        break;
      case 'Escape':
        setIsOpen(false);
        break;
      case 'ArrowDown':
        e.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          // 可以添加键盘导航逻辑
        }
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (isOpen) {
          // 可以添加键盘导航逻辑
        }
        break;
    }
  };

  return (
    <div className={`${styles.customSelect} ${className}`}>
      <div
        ref={selectRef}
        className={`${styles.selectTrigger} ${isOpen ? styles.open : ''} ${disabled ? styles.disabled : ''}`}
        onClick={handleSelectClick}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-disabled={disabled}
      >
        <span className={`${styles.selectValue} ${!selectedOption ? styles.placeholder : ''}`}>
          {displayText}
        </span>
        <span className={`${styles.selectArrow} ${isOpen ? styles.open : ''}`}>
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path
              d="M3 4.5L6 7.5L9 4.5"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </span>
      </div>

      {isOpen && (
        <div
          ref={dropdownRef}
          className={`${styles.selectDropdown} ${isOpen ? styles.show : ''}`}
          role="listbox"
        >
          {options.map((option) => (
            <div
              key={option.value}
              className={`${styles.selectOption} ${option.value === value ? styles.selected : ''}`}
              onClick={(e) => handleOptionClick(option.value, e)}
              role="option"
              aria-selected={option.value === value}
            >
              {option.label}
            </div>
          ))}
          {options.length === 0 && (
            <div className={styles.selectEmpty}>暂无选项</div>
          )}
        </div>
      )}
    </div>
  );
};

export default CustomSelect;
